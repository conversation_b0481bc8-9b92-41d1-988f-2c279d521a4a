/*
 * 玩家控制器 - 等距视角移动系统
 */

using Godot;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家控制器 - 实现等距视角的角色移动
    /// 继承自CharacterBody2D以获得物理移动和碰撞检测功能
    /// </summary>
    public partial class PlayerController : CharacterBody2D
    {

        [Export] public float Speed = 300.0f; // 移动速度（像素/秒）
        public Node2D Player;
        public AnimationPlayer PlayerAnimation;
        public override void _Ready()
        {

            Player = GetNode<Node2D>("/root/PlayerBehavior/Player");
            PlayerAnimation = GetNode<AnimationPlayer>("/root/PlayerBehavior/Player/AnimationPlayer");
        }


        public override void _PhysicsProcess(double delta)
        {
            // 1. 获取输入方向（-1到1之间的值）
            Vector2 inputDirection = Input.GetVector("ui_left", "ui_right", "ui_up", "ui_down");
            Velocity = inputDirection * Speed;
            MoveAndSlide();

            // 根据移动方向播放动画
            if (inputDirection != Vector2.Zero)
            {
                PlayerAnimation.Play("left_move");
                // Player.FlipH = inputDirection.X < 0; // 水平翻转
            }
            else
            {
                // Player.Play("idle");
            }
        }
    }
}
